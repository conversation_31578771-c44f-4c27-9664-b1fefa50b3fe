document.addEventListener('DOMContentLoaded', function() {
    console.log('nouvelle_demande.js loaded successfully');
    // Get form elements
    const dateDebut = document.getElementById('date_debut');
    const dateFin = document.getElementById('date_fin');
    const demiJourDebut = document.getElementById('demi_jour_debut');
    const demiJourFin = document.getElementById('demi_jour_fin');
    const periodeDebut = document.getElementById('periode_debut');
    const periodeFin = document.getElementById('periode_fin');
    const calculJours = document.getElementById('calculJours');
    const nbJours = document.getElementById('nbJours');

    // Enable/disable period selectors
    demiJourDebut.addEventListener('change', function() {
        periodeDebut.disabled = !this.checked;
    });

    demiJourFin.addEventListener('change', function() {
        periodeFin.disabled = !this.checked;
    });

    // Calculate number of days
    function updateDaysCount() {
        if (dateDebut.value && dateFin.value) {
            try {
                const start = new Date(dateDebut.value);
                const end = new Date(dateFin.value);

                if (!isNaN(start) && !isNaN(end) && end >= start) {
                    // Calculate days difference (+1 because inclusive)
                    const diffTime = end - start;
                    let diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

                    if (demiJourDebut.checked) {
                        diffDays -= 0.5;
                    }

                    if (demiJourFin.checked) {
                        diffDays -= 0.5;
                    }

                    nbJours.textContent = diffDays;
                    calculJours.classList.remove('hidden');
                } else {
                    calculJours.classList.add('hidden');
                }
            } catch (e) {
                calculJours.classList.add('hidden');
            }
        } else {
            calculJours.classList.add('hidden');
        }
    }

    // Format date inputs to make sure they're in ISO format (YYYY-MM-DD)
    function formatDateInput(input) {
        input.addEventListener('input', function(e) {
            // Check if the entered value matches the French formats
            const frenchDatePattern1 = /^(\d{2})\/(\d{2})\/(\d{4})$/; // DD/MM/YYYY
            const frenchDatePattern2 = /^(\d{2})-(\d{2})-(\d{4})$/; // DD-MM-YYYY
            const value = e.target.value;

            if (frenchDatePattern1.test(value)) {
                // Convert from DD/MM/YYYY to YYYY-MM-DD
                const matches = value.match(frenchDatePattern1);
                const formattedDate = `${matches[3]}-${matches[2]}-${matches[1]}`;
                e.target.value = formattedDate;
            } else if (frenchDatePattern2.test(value)) {
                // Convert from DD-MM-YYYY to YYYY-MM-DD
                const matches = value.match(frenchDatePattern2);
                const formattedDate = `${matches[3]}-${matches[2]}-${matches[1]}`;
                e.target.value = formattedDate;
            }
        });
    }

    // Apply date formatting to date inputs
    formatDateInput(dateDebut);
    formatDateInput(dateFin);

    // Add blur event to handle manual entries
    dateDebut.addEventListener('blur', function() {
        // Try to convert from DD/MM/YYYY format when losing focus
        const value = this.value;
        if (value && value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
            const parts = value.split(/[\/\-]/);
            if (parts.length === 3) {
                this.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
            }
        }
    });

    dateFin.addEventListener('blur', function() {
        // Try to convert from DD/MM/YYYY format when losing focus
        const value = this.value;
        if (value && value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
            const parts = value.split(/[\/\-]/);
            if (parts.length === 3) {
                this.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
            }
        }
    });

    // Add event listeners for date changes
    dateDebut.addEventListener('change', function() {
        updateDaysCount();
        validateLeaveRequest();
    });
    dateFin.addEventListener('change', function() {
        updateDaysCount();
        validateLeaveRequest();
    });

    // Add validation on type change
    const typeSelect = document.getElementById('type');
    if (typeSelect) {
        typeSelect.addEventListener('change', validateLeaveRequest);
    }

    // Initialize if values are already set
    updateDaysCount();

    // Validation state
    let validationTimeout;
    let isValidating = false;
    let lastValidationResult = null;

    // Function to validate leave request via AJAX
    function validateLeaveRequest() {
        const type = typeSelect ? typeSelect.value : '';
        const startDate = dateDebut.value;
        const endDate = dateFin.value;

        // Clear previous timeout
        if (validationTimeout) {
            clearTimeout(validationTimeout);
        }

        // Hide validation alert initially
        hideValidationAlert();

        // Only validate if we have all required fields
        if (!type || !startDate || !endDate) {
            return;
        }

        // Debounce validation calls
        validationTimeout = setTimeout(() => {
            performValidation(type, startDate, endDate);
        }, 500);
    }

    // Perform the actual validation
    function performValidation(type, startDate, endDate) {
        if (isValidating) return;

        isValidating = true;

        const requestData = {
            type: type,
            date_debut: startDate,
            date_fin: endDate,
            demi_journee: demiJourDebut.checked || demiJourFin.checked,
            demi_type: getDemiType()
        };

        fetch('/demandes/validate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            lastValidationResult = data;

            if (!data.valid) {
                showValidationAlert(data.errors, data.conflicts);
            } else {
                hideValidationAlert();
            }
        })
        .catch(error => {
            console.error('Validation error:', error);
            // Don't show error to user for validation failures
        })
        .finally(() => {
            isValidating = false;
        });
    }

    // Get demi_type value
    function getDemiType() {
        if (demiJourDebut.checked && demiJourFin.checked) {
            return periodeDebut.value + ',' + periodeFin.value;
        } else if (demiJourDebut.checked) {
            return periodeDebut.value;
        } else if (demiJourFin.checked) {
            return periodeFin.value;
        }
        return null;
    }

    // Show conflict modal with enhanced visual feedback
    function showValidationAlert(errors, conflicts) {
        // Highlight conflicting date fields
        highlightConflictingDates(conflicts);

        // Disable submit button
        disableSubmitButton(true);

        // Show the conflict modal
        showConflictModal(errors, conflicts);
    }

    // Show conflict modal
    function showConflictModal(errors, conflicts) {
        const modal = document.getElementById('conflictModal');
        const mainMessage = document.getElementById('conflictMainMessage');
        const cardsContainer = document.getElementById('conflictCardsContainer');

        if (!modal || !mainMessage || !cardsContainer) return;

        // Set main error message
        const primaryMessage = errors.length > 0 ? errors[0] : 'Une demande de congé existe déjà pour cette période.';
        mainMessage.innerHTML = `<p class="text-base">${primaryMessage}</p>`;

        // Clear previous conflict cards
        cardsContainer.innerHTML = '';

        // Handle conflicts with enhanced display
        if (conflicts && conflicts.length > 0) {
            conflicts.forEach((conflict, index) => {
                const conflictCard = createConflictCard(conflict, index + 1);
                cardsContainer.appendChild(conflictCard);
            });
        }

        // Show modal with animation
        modal.classList.remove('hidden');

        // Trigger animation after a brief delay
        setTimeout(() => {
            const modalCard = modal.querySelector('.modal-card');
            if (modalCard) {
                modalCard.classList.remove('scale-95', 'opacity-0');
                modalCard.classList.add('scale-100', 'opacity-100');
            }
        }, 10);

        // Focus management for accessibility
        const closeButton = document.getElementById('closeConflictModal');
        if (closeButton) {
            closeButton.focus();
        }

        // Prevent body scroll
        document.body.style.overflow = 'hidden';
    }

    // Create conflict card element
    function createConflictCard(conflict, index) {
        const card = document.createElement('div');
        card.className = 'bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm';

        const reference = conflict.reference_demande || `REF-${conflict.id}`;
        const startDate = conflict.date_debut_formatted || formatDate(conflict.date_debut);
        const endDate = conflict.date_fin_formatted || formatDate(conflict.date_fin);
        const duration = conflict.duree || 1;
        const type = conflict.type_formatted || conflict.type;
        const status = conflict.statut_formatted || conflict.statut;

        // Create status badge with appropriate color
        const statusColor = getStatusBadgeColor(conflict.statut);

        card.innerHTML = `
            <div class="flex items-start justify-between mb-3">
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center mr-3">
                        <span class="text-red-600 font-bold text-sm">${index}</span>
                    </div>
                    <div>
                        <h6 class="font-semibold text-red-800 text-sm">Conflit #${index}</h6>
                        <p class="text-xs text-red-600">Référence: ${reference}</p>
                    </div>
                </div>
                <span class="px-3 py-1 text-xs font-medium rounded-full ${statusColor}">${status}</span>
            </div>
            <div class="ml-11 space-y-2">
                <div class="flex items-center text-sm">
                    <i class="fas fa-calendar-alt text-red-500 mr-2 w-4"></i>
                    <span class="text-gray-700"><strong>Période:</strong> Du ${startDate} au ${endDate}</span>
                </div>
                <div class="flex items-center text-sm">
                    <i class="fas fa-tag text-red-500 mr-2 w-4"></i>
                    <span class="text-gray-700"><strong>Type:</strong> ${type}</span>
                </div>
                <div class="flex items-center text-sm">
                    <i class="fas fa-clock text-red-500 mr-2 w-4"></i>
                    <span class="text-gray-700"><strong>Durée:</strong> ${duration} jour${duration > 1 ? 's' : ''}</span>
                </div>
            </div>
        `;

        return card;
    }

    // Helper function to get status badge color
    function getStatusBadgeColor(status) {
        switch (status) {
            case 'approuvee':
            case 'Approuvée':
                return 'bg-green-100 text-green-800';
            case 'en_attente_responsable':
            case 'En attente responsable':
                return 'bg-yellow-100 text-yellow-800';
            case 'en_attente_planificateur':
            case 'En attente planificateur':
                return 'bg-orange-100 text-orange-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }

    // Helper function to format date
    function formatDate(dateString) {
        if (!dateString) return 'Date inconnue';
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR');
        } catch (e) {
            return dateString;
        }
    }

    // Highlight conflicting date fields
    function highlightConflictingDates(conflicts) {
        // Remove existing highlights
        clearDateHighlights();

        if (!conflicts || conflicts.length === 0) return;

        // Add red border and background to date inputs
        const dateInputs = [dateDebut, dateFin];
        dateInputs.forEach(input => {
            if (input) {
                input.classList.add('border-red-500', 'bg-red-50');
                input.classList.remove('border-gray-300');
            }
        });

        // Add warning icon to date labels
        const dateLabels = document.querySelectorAll('label[for="date_debut"], label[for="date_fin"]');
        dateLabels.forEach(label => {
            if (!label.querySelector('.conflict-warning')) {
                const warningIcon = document.createElement('i');
                warningIcon.className = 'fas fa-exclamation-triangle text-red-500 ml-1 conflict-warning';
                warningIcon.title = 'Dates en conflit avec une demande existante';
                label.appendChild(warningIcon);
            }
        });
    }

    // Clear date field highlights
    function clearDateHighlights() {
        const dateInputs = [dateDebut, dateFin];
        dateInputs.forEach(input => {
            if (input) {
                input.classList.remove('border-red-500', 'bg-red-50');
                input.classList.add('border-gray-300');
            }
        });

        // Remove warning icons
        const warningIcons = document.querySelectorAll('.conflict-warning');
        warningIcons.forEach(icon => icon.remove());
    }

    // Disable/enable submit button
    function disableSubmitButton(disable) {
        const submitButton = document.querySelector('button[type="submit"]');
        if (submitButton) {
            if (disable) {
                submitButton.disabled = true;
                submitButton.classList.add('opacity-50', 'cursor-not-allowed');
                submitButton.classList.remove('hover:bg-teal-700', 'hover:bg-purple-700');

                // Add disabled text
                const originalText = submitButton.textContent;
                submitButton.setAttribute('data-original-text', originalText);
                submitButton.innerHTML = '<i class="fas fa-ban mr-2"></i>Résolvez les conflits pour continuer';
            } else {
                submitButton.disabled = false;
                submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                submitButton.classList.add('hover:bg-teal-700');

                // Restore original text
                const originalText = submitButton.getAttribute('data-original-text');
                if (originalText) {
                    submitButton.textContent = originalText;
                }
            }
        }
    }

    // Hide validation alert and clear all conflict indicators
    function hideValidationAlert() {
        hideConflictModal();
    }

    // Submit form validation with enhanced blocking
    document.querySelector('form').addEventListener('submit', function(event) {
        try {
            // Always prevent submission initially
            event.preventDefault();

            // Check if submit button is disabled (conflicts exist)
            const submitButton = document.querySelector('button[type="submit"]');
            if (submitButton && submitButton.disabled) {
                // Show prominent error message
                showBlockedSubmissionAlert();
                return false;
            }

            // Check if there are validation conflicts
            if (lastValidationResult && !lastValidationResult.valid) {
                showValidationAlert(lastValidationResult.errors, lastValidationResult.conflicts);

                // Scroll to the validation alert
                const alertContainer = document.getElementById('validationAlert');
                if (alertContainer) {
                    alertContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                return false;
            }

            // Perform final validation check before submission
            const type = typeSelect ? typeSelect.value : '';
            const startDate = dateDebut.value;
            const endDate = dateFin.value;

            if (!type || !startDate || !endDate) {
                alert('Veuillez remplir tous les champs obligatoires.');
                return false;
            }

            // Show loading state
            showSubmissionLoading(true);

            // Perform final server-side validation
            const requestData = {
                type: type,
                date_debut: startDate,
                date_fin: endDate,
                demi_journee: demiJourDebut.checked || demiJourFin.checked,
                demi_type: getDemiType()
            };

            fetch('/demandes/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(data => {
                showSubmissionLoading(false);

                if (!data.valid) {
                    // Final validation failed - show conflicts and block
                    showValidationAlert(data.errors, data.conflicts);
                    return false;
                } else {
                    // Validation passed - submit the form
                    submitFormAfterValidation();
                }
            })
            .catch(error => {
                showSubmissionLoading(false);
                console.error('Final validation error:', error);
                alert('Erreur lors de la validation finale. Veuillez réessayer.');
                return false;
            });

            // At this point, all dates should be in YYYY-MM-DD format due to our conversion handlers
            const startDateObj = new Date(dateDebut.value);
            const endDateObj = new Date(dateFin.value);

            if (isNaN(startDateObj) || isNaN(endDateObj)) {
                event.preventDefault();
                alert(
                    'Format de date invalide. Veuillez utiliser le format JJ/MM/AAAA ou AAAA-MM-JJ.');

                // Try to automatically fix the format if possible
                if (dateDebut.value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
                    const parts = dateDebut.value.split(/[\/\-]/);
                    if (parts.length === 3) {
                        dateDebut.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
                    }
                }

                if (dateFin.value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
                    const parts = dateFin.value.split(/[\/\-]/);
                    if (parts.length === 3) {
                        dateFin.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
                    }
                }
            } else if (endDateObj < startDateObj) {
                event.preventDefault();
                alert('La date de fin doit être postérieure ou égale à la date de début.');
            }
        } catch (e) {
            showSubmissionLoading(false);
            alert('Format de date invalide. Veuillez utiliser le format JJ/MM/AAAA ou AAAA-MM-JJ.');
        }
    });

    // Show blocked submission alert
    function showBlockedSubmissionAlert() {
        const conflictModal = document.getElementById('conflictModal');
        if (conflictModal && !conflictModal.classList.contains('hidden')) {
            // Modal is already visible, shake it for attention
            const modalCard = conflictModal.querySelector('.modal-card');
            if (modalCard) {
                modalCard.classList.add('animate-shake');
                setTimeout(() => {
                    modalCard.classList.remove('animate-shake');
                }, 600);
            }
        } else {
            // Show a prominent blocking message
            const blockingAlert = document.createElement('div');
            blockingAlert.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-red-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 animate-bounce';
            blockingAlert.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-ban mr-3 text-xl"></i>
                    <div>
                        <div class="font-bold">Soumission bloquée</div>
                        <div class="text-sm">Résolvez les conflits de dates avant de continuer</div>
                    </div>
                </div>
            `;

            document.body.appendChild(blockingAlert);

            // Remove after 3 seconds
            setTimeout(() => {
                if (blockingAlert.parentNode) {
                    blockingAlert.parentNode.removeChild(blockingAlert);
                }
            }, 3000);
        }
    }

    // Show/hide submission loading state
    function showSubmissionLoading(show) {
        const submitButton = document.querySelector('button[type="submit"]');
        if (!submitButton) return;

        if (show) {
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Validation en cours...';
            submitButton.classList.add('opacity-75');
        } else {
            // Only re-enable if there are no conflicts
            if (!lastValidationResult || lastValidationResult.valid) {
                submitButton.disabled = false;
                submitButton.classList.remove('opacity-75');
                const originalText = submitButton.getAttribute('data-original-text') || 'Soumettre la demande';
                submitButton.textContent = originalText;
            }
        }
    }

    // Submit form after successful validation
    function submitFormAfterValidation() {
        const form = document.querySelector('form');
        if (form) {
            // Remove the event listener temporarily to allow normal submission
            const newForm = form.cloneNode(true);
            form.parentNode.replaceChild(newForm, form);

            // Submit the form
            newForm.submit();
        }
    }

    // File upload handling (only if elements exist)
    // const fileInput = document.getElementById('justificatif');
    // const fileNameSpan = document.getElementById('file-name');

    // if (fileInput && fileNameSpan) {
    //     fileInput.addEventListener('change', function() {
    //         if (this.files && this.files.length > 0) {
    //             const file = this.files[0];

    //             // Check file size (max 5MB)
    //             if (file.size > 5 * 1024 * 1024) {
    //                 alert('Le fichier est trop volumineux. La taille maximale est de 5 MB.');
    //                 this.value = "";
    //                 fileNameSpan.textContent = 'Aucun fichier sélectionné';
    //                 return;
    //             }

    //             // Display file name
    //             fileNameSpan.textContent = file.name;
    //         } else {
    //             fileNameSpan.textContent = 'Aucun fichier sélectionné';
    //         }
    //     });
    // }
});

// Global Modal Functions
// Hide conflict modal
function hideConflictModal() {
    const modal = document.getElementById('conflictModal');
    if (!modal) return;

    // Animate modal out
    const modalCard = modal.querySelector('.modal-card');
    if (modalCard) {
        modalCard.classList.remove('scale-100', 'opacity-100');
        modalCard.classList.add('scale-95', 'opacity-0');
    }

    // Hide modal after animation
    setTimeout(() => {
        modal.classList.add('hidden');

        // Clear date field highlights
        const dateInputs = document.querySelectorAll('#date_debut, #date_fin');
        dateInputs.forEach(input => {
            if (input) {
                input.classList.remove('border-red-500', 'bg-red-50');
                input.classList.add('border-gray-300');
            }
        });

        // Remove warning icons
        const warningIcons = document.querySelectorAll('.conflict-warning');
        warningIcons.forEach(icon => icon.remove());

        // Re-enable submit button
        const submitButton = document.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
            submitButton.classList.add('hover:bg-teal-700');

            // Restore original text
            const originalText = submitButton.getAttribute('data-original-text');
            if (originalText) {
                submitButton.textContent = originalText;
            }
        }

        // Restore body scroll
        document.body.style.overflow = '';
    }, 300);
}

// Policy Modal Functions
function openPolicyModal() {
    console.log('openPolicyModal function called');
    const modal = document.getElementById('policyModal');
    const modalCard = modal.querySelector('.modal-card');

    if (modal && modalCard) {
        modal.classList.remove('hidden');

        // Trigger animation after a small delay to ensure the modal is visible
        setTimeout(() => {
            modalCard.classList.remove('scale-95', 'opacity-0');
            modalCard.classList.add('scale-100', 'opacity-100');
        }, 10);

        // Prevent body scroll when modal is open
        document.body.style.overflow = 'hidden';
    }
}

function closePolicyModal() {
    const modal = document.getElementById('policyModal');
    const modalCard = modal.querySelector('.modal-card');

    if (modal && modalCard) {
        // Start closing animation
        modalCard.classList.remove('scale-100', 'opacity-100');
        modalCard.classList.add('scale-95', 'opacity-0');

        // Hide modal after animation completes
        setTimeout(() => {
            modal.classList.add('hidden');
            document.body.style.overflow = ''; // Restore body scroll
        }, 300);
    }
}

// Modal event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Policy Modal
    const policyModal = document.getElementById('policyModal');
    if (policyModal) {
        policyModal.addEventListener('click', function(e) {
            if (e.target === policyModal) {
                closePolicyModal();
            }
        });
    }

    // Conflict Modal
    const conflictModal = document.getElementById('conflictModal');
    if (conflictModal) {
        // Click outside to close
        conflictModal.addEventListener('click', function(e) {
            if (e.target === conflictModal) {
                hideConflictModal();
            }
        });

        // Close button event listeners
        const closeButtons = [
            document.getElementById('closeConflictModal'),
            document.getElementById('closeConflictModalBtn')
        ];

        closeButtons.forEach(button => {
            if (button) {
                button.addEventListener('click', hideConflictModal);
            }
        });

        // Go to history button
        const historyButton = document.getElementById('goToHistoryBtn');
        if (historyButton) {
            historyButton.addEventListener('click', function() {
                // Determine the correct history URL based on current page
                let historyUrl = '/mes-demandes';

                if (window.location.pathname.includes('/responsable/')) {
                    historyUrl = '/responsable/mes-demandes';
                } else if (window.location.pathname.includes('/planificateur/')) {
                    historyUrl = '/planificateur/mes-demandes';
                }

                // Close modal and navigate
                hideConflictModal();
                setTimeout(() => {
                    window.location.href = historyUrl;
                }, 300);
            });
        }
    }

    // Global Escape key handler
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            // Check conflict modal first (higher priority)
            const conflictModal = document.getElementById('conflictModal');
            if (conflictModal && !conflictModal.classList.contains('hidden')) {
                hideConflictModal();
                return;
            }

            // Then check policy modal
            const policyModal = document.getElementById('policyModal');
            if (policyModal && !policyModal.classList.contains('hidden')) {
                closePolicyModal();
                return;
            }
        }
    });

    // Focus trap for conflict modal (accessibility)
    // const conflictModal = document.getElementById('conflictModal');
    // if (conflictModal) {
    //     conflictModal.addEventListener('keydown', function(e) {
    //         if (e.key === 'Tab') {
    //             const focusableElements = conflictModal.querySelectorAll(
    //                 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    //             );
    //             const firstElement = focusableElements[0];
    //             const lastElement = focusableElements[focusableElements.length - 1];

    //             if (e.shiftKey) {
    //                 // Shift + Tab
    //                 if (document.activeElement === firstElement) {
    //                     e.preventDefault();
    //                     lastElement.focus();
    //                 }
    //             } else {
    //                 // Tab
    //                 if (document.activeElement === lastElement) {
    //                     e.preventDefault();
    //                     firstElement.focus();
    //                 }
    //             }
    //         }
    //     });
    // }
});
