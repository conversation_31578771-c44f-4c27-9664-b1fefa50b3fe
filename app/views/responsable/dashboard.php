<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord Responsable - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_responsable.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Tableau de bord Responsable</h1>
                    <p class="text-gray-600">Bienvenue, <?= htmlspecialchars($_SESSION['prenom'] . ' ' . $_SESSION['nom']) ?></p>
                </div>
                <div class="flex items-center space-x-3">
                    <?php if ($pendingRequests > 0): ?>
                        <a href="/notifications" class="relative">
                            <i class="fas fa-bell text-indigo-600 text-xl"></i>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center rounded-full"><?= $pendingRequests ?></span>
                        </a>
                    <?php endif; ?>
                    <a href="/profil" class="flex items-center">
                        <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-800 font-bold">
                            <?= strtoupper(substr($_SESSION['prenom'], 0, 1)) ?>
                        </div>
                    </a>
                </div>
            </div>
        </header>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- Aperçu équipe -->
            <div class="card">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Mon équipe</h2>
                    <span class="text-indigo-500"><i class="fas fa-users"></i></span>
                </div>
                <div class="mb-4">
                    <span class="text-3xl font-bold text-gray-900"><?= $teamMembers ?></span>
                    <span class="text-gray-500 ml-1">membres dans l'équipe</span>
                </div>
                <div class="flex items-center mb-1">
                    <div class="w-3 h-3 rounded-full bg-red-400 mr-2"></div>
                    <span class="text-sm text-gray-600"><?= $unavailableMembers ?> membres absents aujourd'hui</span>
                </div>
                <a href="/responsable/team_availability" class="text-indigo-600 font-medium hover:text-indigo-800 flex items-center">
                    <span>Voir le planning</span>
                    <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>

            <!-- Demandes à traiter -->
            <div class="card">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Demandes en attente</h2>
                    <span class="text-yellow-500"><i class="fas fa-clock"></i></span>
                </div>
                <div class="mb-4">
                    <span class="text-3xl font-bold text-gray-900"><?= $pendingRequests ?></span>
                    <span class="text-gray-500 ml-1">demandes à valider</span>
                </div>
                <?php if ($pendingRequests > 0): ?>
                    <a href="/responsable/demandes_approbation" class="text-yellow-600 font-medium hover:text-yellow-800 flex items-center">
                        <span>Traiter les demandes</span>
                        <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                <?php else: ?>
                    <span class="text-green-600 font-medium flex items-center">
                        <i class="fas fa-check-circle mr-1"></i> Tout est à jour
                    </span>
                <?php endif; ?>
            </div>

            <!-- Statistiques -->
            <div class="card">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Activité</h2>
                    <span class="text-green-500"><i class="fas fa-chart-line"></i></span>
                </div>
                <div class="space-y-3">
                    <div>
                        <p class="text-sm text-gray-500">Demandes traitées ce mois</p>
                        <p class="text-2xl font-bold">24</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Temps moyen de traitement</p>
                        <p class="text-xl font-bold">1.2 jours</p>
                    </div>
                </div>
                <a href="/responsable/statistiques" class="mt-4 block text-green-600 text-sm font-medium hover:text-green-800">
                    Statistiques complètes <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>

        <!-- Absences à venir -->
        <div class="card mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-800">Absences à venir</h2>
                <a href="/responsable/team_availability" class="text-sm text-indigo-600 hover:text-indigo-800">Voir le planning complet</a>
            </div>
            <?php if (empty($upcomingAbsences)): ?>
                <p class="text-gray-500">Aucune absence prévue dans les prochains jours</p>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Membre</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Période</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($upcomingAbsences as $absence): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?= htmlspecialchars($absence['prenom'] . ' ' . $absence['nom']) ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= date('d/m/Y', strtotime($absence['date_debut'])) ?>
                                            <?php if ($absence['date_debut'] !== $absence['date_fin']): ?>
                                                au <?= date('d/m/Y', strtotime($absence['date_fin'])) ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-indigo-100 text-indigo-800">
                                            <?= htmlspecialchars($absence['type']) ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <!-- Demandes à valider -->
        <div class="card">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-800">Demandes à valider</h2>
                <?php if ($pendingRequests > 0): ?>
                    <a href="/demandes-a-valider" class="text-sm text-indigo-600 hover:text-indigo-800">Voir toutes les demandes</a>
                <?php endif; ?>
            </div>
            <?php if (empty($requestsToApprove)): ?>
                <p class="text-gray-500">Aucune demande en attente de validation</p>
            <?php else: ?>
                <div class="space-y-4">
                    <?php foreach ($requestsToApprove as $request): ?>
                        <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex justify-between items-start">
                                <div>
                                    <p class="font-medium text-gray-900">
                                        <?= htmlspecialchars($request['prenom'] . ' ' . $request['nom']) ?>
                                    </p>
                                    <p class="text-sm text-gray-500">
                                        <?= htmlspecialchars($request['type']) ?> :
                                        <?= date('d/m/Y', strtotime($request['date_debut'])) ?>
                                        <?php if ($request['date_debut'] !== $request['date_fin']): ?>
                                            au <?= date('d/m/Y', strtotime($request['date_fin'])) ?>
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="bg-green-100 text-green-700 px-3 py-1 rounded-md hover:bg-green-200 text-sm">
                                        <i class="fas fa-check"></i> Approuver
                                    </button>
                                    <button class="bg-red-100 text-red-700 px-3 py-1 rounded-md hover:bg-red-200 text-sm">
                                        <i class="fas fa-times"></i> Refuser
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
    </script>
</body>
</html>
